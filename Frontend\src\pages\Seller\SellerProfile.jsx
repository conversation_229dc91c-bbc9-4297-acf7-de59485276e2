import React from "react";
import { FaUser } from "react-icons/fa";
import SellerLayout from "../../components/seller/SellerLayout";
import DynamicHeading from "../../components/common/DynamicHeading";
import "../../styles/SellerProfile.css";

const SellerProfile = () => {
  return (
    <SellerLayout>
      <DynamicHeading icon={<FaUser />} title="Profile" />

      <div className="SellerProfile__content">
        <p>Manage your seller profile information here.</p>
      </div>
    </SellerLayout>
  );
};

export default SellerProfile;
