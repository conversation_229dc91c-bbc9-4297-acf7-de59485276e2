/* SellerSidebar Component Styles */
.SellerSidebar {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  width: 100%;
}

.SellerSidebar__container {
  display: flex;
  flex-direction: column;
  padding: var(--basefont);
}

.SellerSidebar__menu {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
}

.SellerSidebar__item {
  display: flex;
  align-items: center;
  padding: var(--smallfont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--secondary-color);
  font-weight: 500;
}

.SellerSidebar__item:hover {
  color: var(--btn-color);
}

.SellerSidebar__item.active {
  background-color: var(--btn-color);
  color: var(--white);
  font-weight: 600;
}

.SellerSidebar__icon {
  margin-right: var(--smallfont);
  font-size: var(--heading6);
}

.SellerSidebar__logout {
  margin-top: var(--heading6);
  border-top: 1px solid var(--light-gray);
  padding-top: var(--heading6);
  color: var(--btn-color);
}

/* Responsive styles */
@media (max-width: 768px) {
  .SellerSidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-index-modal);
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
  }

  .SellerSidebar.open {
    display: flex;
  }

  .SellerSidebar__container {
    width: 80%;
    max-width: 300px;
    height: 100%;
    background-color: var(--white);
    overflow-y: auto;
  }
}
