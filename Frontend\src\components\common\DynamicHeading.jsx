import React from "react";
import "../../styles/DynamicHeading.css";

/**
 * Dynamic Heading Component
 * A reusable heading component that matches the dashboard tab UI design
 * with light pink background, icon, text, rounded left edge, and bottom border
 *
 * Features:
 * - Light pink background (#fddcdc)
 * - Rounded left edge with angled right edge using clip-path
 * - Icon + text layout with proper spacing
 * - Bottom border for section separation
 * - Fully responsive design
 * - Uses CSS variables from index.css
 * - Hover effects for interactive feel
 *
 * Usage Examples:
 * <DynamicHeading icon={<MdDashboard />} title="Dashboard" />
 * <DynamicHeading icon={<FaUser />} title="Profile" />
 * <DynamicHeading icon={<FaShoppingCart />} title="Orders" />
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.icon - Icon component to display
 * @param {string} props.title - Heading title text
 * @param {string} props.className - Additional CSS class names
 */
const DynamicHeading = ({ icon, title, className = "" }) => {
  return (
    <div className={`DynamicHeading ${className}`}>
      <div className="DynamicHeading__border">
        <h2 className="DynamicHeading__title">
          {icon && <span className="DynamicHeading__icon">{icon}</span>}
          <span className="DynamicHeading__text">{title}</span>
        </h2>
      </div>
    </div>
  );
};

export default DynamicHeading;
