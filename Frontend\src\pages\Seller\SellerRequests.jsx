import React from "react";
import { FaShoppingCart } from "react-icons/fa";
import SellerLayout from "../../components/seller/SellerLayout";
import DynamicHeading from "../../components/common/DynamicHeading";
import "../../styles/SellerRequests.css";

const SellerRequests = () => {
  return (
    <SellerLayout>
      <DynamicHeading icon={<FaShoppingCart />} title="Orders" />

      <div className="SellerRequests__content">
        <p>View and manage your customer orders here.</p>
      </div>
    </SellerLayout>
  );
};

export default SellerRequests;
